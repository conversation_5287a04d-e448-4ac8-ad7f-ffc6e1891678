"""
Source management for Update Data module.

This module handles all source selection logic including:
- File and folder selection
- Folder monitoring
- File discovery and enrichment
- Source option changes

Extracted from the monolithic UpdateDataPresenter as part of the decomposition refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING

from ....core.services.logger import log
from ..config.ud_config import ud_config
from ..config.ud_keys import UpdateDataKeys
from ..config.option_types import SourceOptions, SaveOptions
from ..services.file_info_service import FileInfoService
from ..services.events_data import SourceDiscoveredEvent, FileDisplayUpdateEvent
from ..services.local_event_bus import ViewEvents

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_manager import UpdateDataState
    from .widget_state_manager import WidgetStateManager


class SourceManager:
    """
    Manages all source selection and folder monitoring logic.

    This class handles:
    - File and folder selection dialogs
    - File enrichment using FileInfoService
    - Folder monitoring integration
    - Source option changes
    """

    def __init__(self, view: 'IUpdateDataView', state: 'UpdateDataState',
                 widget_state_manager: 'WidgetStateManager', folder_monitor_service,
                 local_bus, info_bar_service):
        """
        Initialize the source manager.

        Args:
            view: The view interface
            state: The presenter state object
            widget_state_manager: Manager for widget state updates
            folder_monitor_service: Service for folder monitoring
            local_bus: Local event bus for communication
            info_bar_service: Service for info bar messages
        """
        self.view = view
        self.state = state
        self.widget_state_manager = widget_state_manager
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service

        # Legacy state tracking (will be migrated to state manager)
        self.selected_source = None
        self._updating_source_option = False

    def enrich_file_info(self, file_paths):
        """
        Enrich file information using FileInfoService.

        Args:
            file_paths: List of file paths to enrich

        Returns:
            List of enriched file information dictionaries
        """
        # Use FileInfoService to get enriched file info
        enriched_info = FileInfoService.discover_files(file_paths)

        # Log the enriched info for debugging
        log.debug(f"Enriched file info: {enriched_info}")

        return enriched_info

    def handle_source_select(self, selection_type: str):
        """Handle source selection request."""
        log.debug(f"Source selection requested for type: {selection_type}")
        initial_dir = str(
            ud_config.get_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
        )

        if selection_type == SourceOptions.SELECT_FOLDER.value:
            folder = self.view.show_folder_dialog(
                "Select Source Folder", initial_dir=initial_dir
            )
            if not folder:
                return

            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, folder)

            # Get all supported files from the folder
            extensions = ud_config.get_allowed_file_extensions()
            file_paths = [
                str(p)
                for p in Path(folder).iterdir()
                if p.is_file() and p.suffix.lower() in extensions
            ]

            if not file_paths:
                ext_list = ", ".join(extensions)
                self.view.show_error(f"No supported files found in selected folder. Looking for: {ext_list}")
                return

            # Enrich file information using FileInfoService
            enriched_files = self.enrich_file_info(file_paths)

            # Create a dictionary with source information
            self.selected_source = {
                "type": "folder",
                "path": folder,
                "file_paths": file_paths,
            }

            # Update state
            self.state.source_configured = True
            self.state.source_type = "folder"
            self.state.source_path = folder
            self.state.selected_files = enriched_files  # Use enriched data
            self.state.update_can_process()
            self.widget_state_manager.sync_state_to_view()

        elif selection_type == SourceOptions.SELECT_FILES.value:
            files = self.view.show_files_dialog(
                "Select Source Files", initial_dir=initial_dir
            )
            if not files:
                return

            # Save the directory of the first file for next time
            last_dir = os.path.dirname(files[0])
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, last_dir)

            # Enrich file information using FileInfoService
            enriched_files = self.enrich_file_info(files)

            # Create a dictionary with source information
            self.selected_source = {"type": "files", "file_paths": files}

            # Update state
            self.state.source_configured = True
            self.state.source_type = "files"
            self.state.source_path = last_dir
            self.state.selected_files = enriched_files  # Use enriched data
            self.state.update_can_process()
            self.widget_state_manager.sync_state_to_view()

        else:
            self.view.show_error(f"Unknown source selection type: {selection_type}")
            return

        # MIGRATION: Emit events instead of direct view manipulation
        if self.selected_source:
            # Emit source discovered event for state coordinator
            source_data = SourceDiscoveredEvent(
                source_type=self.selected_source["type"],
                files=self.selected_source["file_paths"],
                path=self.selected_source.get("path", ""),
                count=len(self.selected_source["file_paths"])
            )
            self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value, source_data)

            # Emit files display update event for view
            files_display_data = FileDisplayUpdateEvent(
                files=self.state.selected_files,  # Use enriched data for the view
                source_path=self.selected_source.get("path", os.path.dirname(self.selected_source["file_paths"][0]) if self.selected_source["file_paths"] else "")
            )
            self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value, files_display_data)

            # If save option is "Same as source", update the save location now
            if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                # This will need to be handled by the presenter or archive manager
                pass

    def handle_folder_monitor_file_discovered(self, file_path: str, source_folder: str) -> None:
        """Handle file discovery from folder monitor service

        Args:
            file_path: Path to the discovered file
            source_folder: Path to the folder where the file was discovered
        """
        log.info(f"File discovered by folder monitor: {file_path}")

        # Check if the file path is already in our selected files
        file_paths_list = [f.get('path') for f in self.state.selected_files] if isinstance(self.state.selected_files, list) else []

        if file_path not in file_paths_list:
            # Enrich the file information using FileInfoService
            enriched_files = self.enrich_file_info([file_path])

            if enriched_files:
                # Add the enriched file info to the state
                if isinstance(self.state.selected_files, list):
                    self.state.selected_files.extend(enriched_files)
                else:
                    self.state.selected_files = enriched_files

                # Update the view with enriched file info using the interface method
                self.view.display_enriched_file_info(self.state.selected_files)

                # Update processing state
                self.state.update_can_process()
                self.widget_state_manager.sync_state_to_view()

                # Update guide pane
                self.widget_state_manager.update_guide_pane_for_files()

                # Emit files display update event for view
                files_display_data = FileDisplayUpdateEvent(
                    files=self.state.selected_files,  # Use enriched data for the view
                    source_path=source_folder
                )
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value, files_display_data)

    def toggle_folder_monitoring(self, folder_path: str, enabled: bool) -> None:
        """Toggle folder monitoring for a specific folder

        Args:
            folder_path: Path to the folder
            enabled: Whether monitoring should be enabled
        """
        if enabled:
            # Start monitoring the folder
            self.folder_monitor_service.add_folder(folder_path)
            log.info(f"Started folder monitoring for: {folder_path}")
        else:
            # Stop monitoring the folder
            self.folder_monitor_service.remove_folder(folder_path)
            log.info(f"Stopped folder monitoring for: {folder_path}")

        # Start the service if not already running
        if enabled and not hasattr(self, '_folder_monitor_started'):
            self.folder_monitor_service.start()
            self._folder_monitor_started = True

    def handle_monitor_folder_change(self, enabled: bool):
        """Handle folder monitoring toggle from guide pane

        Args:
            enabled: Whether monitoring should be enabled
        """
        if not self.state.source_path:
            log.warning("Cannot toggle folder monitoring: no folder selected")
            return

        self.toggle_folder_monitoring(self.state.source_path, enabled)

    def handle_toggle_folder_monitoring(self, folder_path: str, enabled: bool):
        """Handle toggle folder monitoring request from file pane.

        This controls the FolderMonitorService based on UI toggle state.

        Args:
            folder_path: Path to the folder to monitor
            enabled: Whether monitoring should be enabled
        """
        try:
            if enabled:
                # Start monitoring
                self.folder_monitor_service.add_folder(folder_path)
                self.info_bar_service.publish_message(f"Folder monitoring enabled for: {folder_path}")
                log.info(f"Started folder monitoring for: {folder_path}")
            else:
                # Stop monitoring
                self.folder_monitor_service.remove_folder(folder_path)
                self.info_bar_service.publish_message(f"Folder monitoring disabled for: {folder_path}")
                log.info(f"Stopped folder monitoring for: {folder_path}")

            # Sync UI state
            self.widget_state_manager.sync_state_to_view()
        except Exception as e:
            log.error(f"Error toggling folder monitoring: {e}")
            self.info_bar_service.publish_message(f"Error toggling folder monitoring: {str(e)}")
            # Reset UI state on error
            if hasattr(self.view, 'file_pane') and hasattr(self.view.file_pane, 'monitor_folder_checkbox'):
                self.view.file_pane.monitor_folder_checkbox.setChecked(False)

    def handle_source_option_change(self, option: str):
        """Handle source option change."""
        # Skip processing if we're programmatically updating the option
        if self._updating_source_option:
            return

        log.debug(f"Source option changed to: {option}")

        # Store the new option in configuration
        ud_config.set_value(UpdateDataKeys.Source.LAST_SOURCE_OPTION, option)

        # Reset source state when option changes
        self.state.source_configured = False
        self.state.selected_files.clear()
        self.state.update_can_process()
        self.widget_state_manager.sync_state_to_view()

        # Clear any existing source selection
        self.selected_source = None

        # Update info bar
        self.info_bar_service.publish_message("Source option changed. Please select your source.")

    def on_file_discovered(self, event_data: dict):
        """Handle auto-import file discovery from event bus - ONLY adds to display."""
        filepath = event_data.get("filepath", "")
        source = event_data.get("source", "")

        if source == "auto_import" and filepath:
            log.debug(f"Auto-import file discovered: {filepath}")

            # Add to selected files if not already present
            if filepath not in self.state.selected_files:
                self.state.selected_files.append(filepath)

                # Update file display
                self.view.center_display.set_files(self.state.selected_files)

                # Update guide pane
                self.widget_state_manager.update_guide_pane()

                log.debug(f"Added auto-discovered file to display: {os.path.basename(filepath)}")
