"""
Widget state management for Update Data module.

This module handles all UI synchronization logic including:
- State-to-view synchronization
- Guide pane updates
- UI state management
- Widget configuration

Extracted from the monolithic UpdateDataPresenter as part of the decomposition refactoring.
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_manager import UpdateDataState


class WidgetStateManager:
    """
    Manages UI synchronization and widget state updates.

    This class handles all direct view updates and UI state management,
    keeping the main presenter focused on coordination.
    """

    def __init__(self, view: 'IUpdateDataView', state: 'UpdateDataState',
                 info_bar_service, folder_monitor_service):
        """
        Initialize the widget state manager.

        Args:
            view: The view interface for UI updates
            state: The presenter state object
            info_bar_service: Service for info bar messages
            folder_monitor_service: Service for folder monitoring
        """
        self.view = view
        self.state = state
        self.info_bar_service = info_bar_service
        self.folder_monitor_service = folder_monitor_service

    def sync_state_to_view(self) -> None:
        """
        Sync presenter state to view.

        MVP pattern: Presenter state is source of truth, view reflects state.
        This replaces the archived view_context_manager approach.
        """
        # Update process button
        self.view.set_process_button_text(self.state.process_button_text)
        self.view.set_process_enabled(self.state.can_process)

        # Update save controls
        self.view.set_save_select_enabled(self.state.destination_configured)

        # Update source display
        if self.state.source_configured:
            # Use the interface method to set source option
            self.view.set_source_option(f"{self.state.source_type}: {self.state.source_path}")

            # Update file display with all selected files
            self.view.center_display.set_files(self.state.selected_files)

        # Update save path display
        if self.state.save_path:
            self.view.set_save_path(self.state.save_path)

        # Show error if present
        if self.state.error_message:
            self.view.show_error(self.state.error_message)

        # Update info bar with status
        self.info_bar_service.publish_message(self.state.status_message)

        # Update guide pane with contextual message
        self.update_guide_pane()

    def update_guide_pane(self):
        """Update guide pane with contextual message based on current state."""
        if not hasattr(self.view, 'guide_pane'):
            return

        # Determine the appropriate message based on state
        if self.state.processing:
            # Show processing message if available
            if hasattr(self.state, 'current_file_index') and hasattr(self.state, 'total_files'):
                self.view.guide_pane.set_state('processing', {
                    'current': self.state.current_file_index,
                    'total': self.state.total_files
                })
            else:
                self.view.guide_pane.display("Processing files...", 'processing')
        elif self.state.can_process:
            # Ready to process - show monitor folder option
            self.view.guide_pane.set_state('ready', {'count': len(self.state.selected_files)})
            # Add monitor folder checkbox with current monitoring status
            if self.state.source_type == "folder":
                is_monitored = self.folder_monitor_service.is_folder_monitored(self.state.source_path)
                self.view.guide_pane.show_folder_monitoring_option(is_monitored)
        elif self.state.source_configured and self.state.destination_configured:
            # Both configured but can't process (no files?)
            if len(self.state.selected_files) == 0:
                self.view.guide_pane.set_state('warning')
                # Still show monitor folder option even if no files currently
                if self.state.source_type == "folder":
                    is_monitored = self.folder_monitor_service.is_folder_monitored(self.state.source_path)
                    self.view.guide_pane.show_folder_monitoring_option(is_monitored)
            else:
                self.view.guide_pane.set_state('ready', {'count': len(self.state.selected_files)})
                # Add monitor folder checkbox
                if self.state.source_type == "folder":
                    is_monitored = self.folder_monitor_service.is_folder_monitored(self.state.source_path)
                    self.view.guide_pane.show_folder_monitoring_option(is_monitored)
        elif self.state.source_configured:
            # Source configured, show file count and context
            context = {
                'count': len(self.state.selected_files),
                'source_path': self.state.source_path,
                'source_type': self.state.source_type
            }
            if self.state.source_type == "folder":
                self.view.guide_pane.set_state('folder_selected', context)
            else:
                self.view.guide_pane.set_state('files_selected', context)
        else:
            # Initial state
            self.view.guide_pane.set_state('initial')

    def update_guide_pane_for_folder(self, folder_path: str) -> None:
        """Update guide pane for folder selection with monitoring checkbox

        Args:
            folder_path: Path to the selected folder
        """
        # Check if folder is being monitored
        is_monitored = self.folder_monitor_service.is_folder_monitored(folder_path)

        # Show folder monitoring checkbox in guide pane
        if hasattr(self.view, 'guide_pane'):
            self.view.guide_pane.show_folder_monitoring_option(is_monitored)

    def update_guide_pane_for_files(self) -> None:
        """Update guide pane based on selected files"""
        file_count = len(self.state.selected_files)

        if file_count > 0:
            # Update guide pane with file count
            if hasattr(self.view, 'guide_pane'):
                if self.state.source_type == "folder":
                    self.view.guide_pane.set_state('folder_selected', {'count': file_count})
                else:
                    self.view.guide_pane.set_state('files_selected', {'count': file_count})
        else:
            # No files selected
            if hasattr(self.view, 'guide_pane'):
                self.view.guide_pane.set_state('warning', {'message': 'No compatible files found'})
