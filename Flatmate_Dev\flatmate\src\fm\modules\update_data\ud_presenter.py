#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.

This is the main presenter class that has been decomposed into specialized managers:
- StateManager: Handles all presenter state
- WidgetStateManager: Handles UI synchronization
- SourceManager: Handles source selection and folder monitoring
- ArchiveManager: Handles save location and archive options
- ProcessingManager: Handles file processing and events

The presenter now acts primarily as a coordinator between these managers.
"""

from typing import Optional
import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import log
from ...core.services.folder_monitor_service import folder_monitor_service
from .services.file_info_service import FileInfoService

# InfoBarService imported locally to avoid circular imports
# configure_auto_import imported locally to avoid circular imports
from ..base.base_presenter import BasePresenter
from .interface import IUpdateDataView
from .config.ud_config import ud_config
from .config.ud_keys import UpdateDataKeys
from .config.option_types import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents

# MIGRATION: Import local event bus for event-driven architecture
from .services.local_event_bus import update_data_local_bus, ViewEvents, setup_global_event_bridges
# Import event dataclasses for type-safe event handling
from .services.events_data import *

# Import state management from new location
from ._presenter.state_manager import UpdateDataState, StateManager
from ._presenter.file_manager import FileManager
from ._presenter.processing_manager import ProcessingManager


class UpdateDataPresenter(BasePresenter):
    """
    Decomposed Update Data presenter.
    
    This presenter has been refactored from a monolithic class into a coordinated
    system of specialized managers. The presenter now primarily handles:
    - Manager instantiation and dependency injection
    - Signal routing to appropriate managers
    - Inter-manager coordination (temporary - should be refactored)
    - Module lifecycle management
    
    The actual business logic has been moved to specialized managers:
    - StateManager: All presenter state
    - WidgetStateManager: UI synchronization
    - SourceManager: Source selection and monitoring
    - ArchiveManager: Save location and options
    - ProcessingManager: File processing and events
    """

    def __init__(self, main_window, gui_config, gui_keys):
        """Initialize the decomposed presenter with manager coordination."""
        super().__init__(main_window, gui_config, gui_keys)

        # Set debug log level for Update Data module
        from ...core.services.logger import log
        
        # Directly set debug level
        log.set_level("DEBUG")
        log.debug("log level set to debug in ud_presenter.py")
        log.debug("UPDATE_DATA: Debug logging enabled for console output")
        
        # Initialize state manager first
        self.state = UpdateDataState()

        # Initialize services
        from ...gui.services.info_bar_service import InfoBarService
        self.info_bar_service = InfoBarService.get_instance()

        # MIGRATION: Add local event bus for event-driven architecture
        self.local_bus = update_data_local_bus

        # State tracking (legacy - will be migrated to ui_state)
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self._updating_source_option = False  # Flag to prevent signal loops

        # View manager for morphic UI
        # self.view_manager = UpdateDataViewManager()  # Missing - in archive

        # Initialize folder monitor service integration
        self.folder_monitor_service = folder_monitor_service
        # Callback will be registered in _connect_signals after source_manager is created

        # SimpleStateCoordinator will be initialized in _connect_signals after view creation
        self.state_coordinator = None

    def _create_view(self) -> IUpdateDataView:
        """Create the view instance. Called once during setup."""
        # Import the concrete implementation locally to avoid circular dependencies
        from .ud_view import UpdateDataView

        # Return the concrete view, but the presenter will only interact with it
        # through the IUpdateDataView interface.
        return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)

    def _connect_signals(self):
        """Connect view interface signals to handlers. Called once during setup."""
        # Initialize consolidated StateManager now that view is available
        self.state_manager = StateManager(
            self.view,
            self.info_bar_service,
            self.folder_monitor_service
        )
        # Update state reference to use consolidated manager
        self.state = self.state_manager.state

        # Initialize FileManager (consolidates SourceManager + ArchiveManager)
        self.file_manager = FileManager(
            self.view,
            self.state_manager,
            self.folder_monitor_service,
            self.local_bus,
            self.info_bar_service
        )

        # Register folder monitor callback now that file_manager is available
        self.folder_monitor_service.register_callback(self.file_manager.handle_folder_monitor_file_discovered)

        # Initialize ProcessingManager now that view is available
        self.processing_manager = ProcessingManager(
            self.view,
            self.state,
            self.info_bar_service,
            self.local_bus
        )

        # Connect to interface signals (high-level domain events)
        # No more method wrapping - FileManager handles internal coordination
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.source_select_requested.connect(self.file_manager.handle_source_select)
        self.view.save_select_requested.connect(self.file_manager.handle_save_select)
        self.view.source_option_changed.connect(self.file_manager.handle_source_option_change)
        self.view.save_option_changed.connect(self.file_manager.handle_save_option_change)
        self.view.process_clicked.connect(self.processing_manager.handle_process)

        # Connect folder monitoring toggle signal from file pane
        if hasattr(self.view, 'file_pane') and hasattr(self.view.file_pane, 'publish_toggle_folder_monitoring_requested'):
            self.view.file_pane.publish_toggle_folder_monitoring_requested.connect(
                self.file_manager.toggle_folder_monitoring
            )
            log.debug("Connected file pane folder monitoring toggle signal")

        # Connect folder monitoring toggle signal from guide pane
        if hasattr(self.view, 'guide_pane') and hasattr(self.view.guide_pane, 'publish_toggle_folder_monitoring_requested'):
            self.view.guide_pane.publish_toggle_folder_monitoring_requested.connect(
                self.file_manager.handle_monitor_folder_change
            )
            log.debug("Connected guide pane folder monitoring toggle signal")
        self.view.update_database_changed.connect(self._handle_update_database_change)

        # Connect guide pane signals for monitor folder checkbox
        if hasattr(self.view, 'guide_pane'):
            self.view.guide_pane.message_changed.connect(self._handle_guide_pane_message)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        self.state_coordinator = None  #
        setup_global_event_bridges()

        log.debug("Signals connected and event bridges set up")

        # Subscribe to Update Data events using processing manager
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self.processing_manager.on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self.processing_manager.on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        # Subscribe to local file display update events
        from .services.local_event_bus import update_data_local_bus
        from ._view.state.view_events import ViewEvents
        update_data_local_bus.subscribe(
            ViewEvents.FILE_DISPLAY_UPDATE.value,
            self.view.update_files_display
        )
        log.debug("Added FILE_DISPLAY_UPDATE event subscription")

    def _refresh_content(self, **params):
        """Refresh update data content when shown.

        This method is called every time the module becomes visible.
        It handles view state setup and configuration.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing UpdateData content")

        # Set the source option from config to remember user's last choice
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)

        # Configure view based on database mode using interface methods
        is_database_mode = self.view.get_update_database()
        # TODO: is_data_base... not accessed?
        # Set initial process button state
        self.view.set_process_button_text(self.state.process_button_text)

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()

        # Show the InfoBar with appropriate message
        self.info_bar_service.show()
        self.info_bar_service.publish_message(
            "Select source files or folder to begin.", "INFO"
        )

        self._setup_view_from_config()

        # Sync initial state to view (including guide pane)
        self.state_manager.sync_state_to_view()

        log.debug("UpdateData content refresh complete")

    def _handle_guide_pane_message(self, message: str):
        """Handle guide pane messages including monitor folder checkbox changes."""
        if message.startswith("checkbox_changed:monitor_folder:"):
            # Extract the checkbox state (0 = unchecked, 2 = checked)
            state = message.split(":")[-1]
            is_checked = state == "2"

            # Update the monitor folder setting
            self.source_manager.handle_monitor_folder_change(is_checked)

            log.debug(f"Monitor folder checkbox changed: {is_checked}")

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change - drives UI morphing."""
        # Store the state in configuration
        ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

        # Configure UI based on mode using view manager, but preserve checkbox state
        # TODO: view_manager is missing - needs to be restored from archive
        # self.view_manager.configure_view_for_workflow_preserve_checkbox(
        #     self.view,
        #     is_database_mode=checked,
        #     preserve_checkbox_state=True
        # )

        # Update button text based on mode (NO MORE buttons_widget!)
        if checked:
            self.view.set_process_text("Update Database")
            status_msg = "Database mode: Files will be imported to database"
        else:
            self.view.set_process_text("Process Files")
            status_msg = "File utility mode: Files will be processed without database updates"

        self.info_bar_service.publish_message(status_msg)

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        global_event_bus.publish(Events.REQUEST_VIEW_TRANSITION.name, target_view)

    def cleanup(self):
        """Clean up before being replaced."""
        # Unsubscribe from events using processing manager
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self.processing_manager.on_processing_started
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self.processing_manager.on_processing_stats
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()

    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass
            