# Canonical File Paths Implementation Plan

Based on the analysis in `FILE_DISPLAY_CHAIN_ANALYSIS.md` and the canonical file paths handling requirements, this document outlines a concrete implementation plan to ensure consistent file path handling throughout the Update Data module.

## Core Principles

1. **Single Source of Truth**: The `file_pane` component will be the authoritative source for which files get processed
2. **Consistent Data Types**: All file path data will be maintained as lists throughout the chain
3. **Clear Responsibility**: The `FileManager` centralizes all file discovery logic
4. **Robust Error Handling**: Defensive programming to prevent type mismatches

## Implementation Steps

### 1. Update FileManager Class

```python
class FileManager:
    # ... existing code ...
    
    def __init__(self, view, state_manager, folder_monitor_service, local_bus, info_bar_service):
        # ... existing initialization ...
        
        # Add canonical file paths list
        self.file_paths_list = []
        
    def _select_files(self):
        """Select individual files using file dialog."""
        try:
            # ... existing file dialog code ...
            
            if file_paths:
                # Store canonical file paths list
                self.file_paths_list = file_paths
                log.debug(f"[FILE_MANAGER] Updated canonical file_paths_list with {len(file_paths)} files")
                
                # Update state
                self.state.selected_files = file_paths
                self.state.source_type = 'files'
                self.selected_source = file_paths
                
                # Display files in view
                self.view.display_selected_source({
                    'type': 'files',
                    'count': len(file_paths),
                    'paths': file_paths
                })
                
                # Pass file paths to file_pane
                if hasattr(self.view, 'center_panel'):
                    log.debug(f"[FILE_MANAGER] Passing file_paths_list to center_panel.set_files()")
                    self.view.center_panel.set_files(self.file_paths_list, "")
                
                # Update state
                self.state.update_can_process()
                
        except Exception as e:
            log.error(f"Error selecting files: {e}")
            
    def _select_folder(self):
        """Select a folder using folder dialog."""
        try:
            # ... existing folder dialog code ...
            
            if folder_path:
                # Discover files in folder
                self._discover_files_in_folder(folder_path)
                
                # Note: file_paths_list is updated in _discover_files_in_folder
                
        except Exception as e:
            log.error(f"Error selecting folder: {e}")
            
    def _discover_files_in_folder(self, folder_path: str):
        """Discover CSV files in the selected folder."""
        try:
            # ... existing file discovery code ...
            
            # Store canonical file paths list
            self.file_paths_list = discovered_files
            log.debug(f"[FILE_MANAGER] Updated canonical file_paths_list with {len(discovered_files)} files from folder")
            
            # Update state
            self.state.selected_folder = folder_path
            self.state.source_type = 'folder'
            self.selected_source = folder_path
            
            # Display files in view
            self.view.display_selected_source({
                'type': 'folder',
                'path': folder_path,
                'count': len(discovered_files)
            })
            
            # Pass file paths to file_pane
            if hasattr(self.view, 'center_panel'):
                log.debug(f"[FILE_MANAGER] Passing file_paths_list to center_panel.set_files()")
                self.view.center_panel.set_files(self.file_paths_list, folder_path)
                
            # Update state
            self.state.update_can_process()
            
        except Exception as e:
            log.error(f"Error discovering files in folder: {e}")
            
    def get_files_for_processing(self):
        """
        Get the current list of files for processing.
        
        This method fetches the current file list directly from file_pane,
        which is the authoritative source for which files get processed.
        """
        try:
            if hasattr(self.view, 'file_pane'):
                current_files = self.view.file_pane.get_current_files()
                log.debug(f"[FILE_MANAGER] Retrieved {len(current_files)} files from file_pane for processing")
                return current_files
            else:
                log.debug(f"[FILE_MANAGER] No file_pane available, using stored file_paths_list")
                return self.file_paths_list
                
        except Exception as e:
            log.error(f"Error getting files for processing: {e}")
            return self.file_paths_list  # Fallback to stored list
```

### 2. Update FilePaneClass

```python
class FilePane(QWidget):
    # ... existing code ...
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set files to display in the file pane."""
        log.debug(f"[FILE_PANE] Received set_files call - files: {len(files) if files else 0}, source_dir: {source_dir}")
        
        # Ensure files is a list (defensive programming)
        if not isinstance(files, list):
            log.warning(f"[FILE_PANE] Expected list of files but got {type(files)}: {files}")
            files = [files] if files else []
            
        log.debug(f"[FILE_PANE] Files list: {files}")
        
        if source_dir:
            log.debug(f"[FILE_PANE] Setting source path: {source_dir}")
            self.set_source_path(source_dir)
            
        log.debug(f"[FILE_PANE] Calling file_browser.set_files()")
        self.file_browser.set_files(files, source_dir)
        log.debug(f"[FILE_PANE] file_browser.set_files() completed")
        
    def get_current_files(self) -> list:
        """
        Get the current list of files in the file pane.
        
        This is the authoritative source for which files get processed.
        Returns:
            List of file paths currently displayed/selected in the file pane
        """
        files = self.file_browser.get_files()
        log.debug(f"[FILE_PANE] Returning {len(files)} files as current file list")
        return files
```

### 3. Update FileBrowser Class

```python
class FileBrowser(QWidget):
    # ... existing code ...
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set files to display in the browser."""
        # Ensure files is a list (defensive programming)
        if not isinstance(files, list):
            log.warning(f"[FILE_BROWSER] Expected list of files but got {type(files)}: {files}")
            files = [files] if files else []
            
        log.debug(f"[FILE_BROWSER] Setting {len(files)} files from {source_dir}")
        
        # Store the files list
        self._files = files
        
        # Update UI
        self._update_file_list_display()
        
    def get_files(self) -> list:
        """Get the current list of files in the browser."""
        return self._files
        
    def _update_file_list_display(self):
        """Update the file list display in the UI."""
        # ... existing UI update code ...
```

### 4. Update ProcessingManager

```python
class ProcessingManager:
    # ... existing code ...
    
    def handle_process(self):
        """Handle process button click."""
        try:
            # Get files directly from file_pane (source of truth)
            files_to_process = self.file_manager.get_files_for_processing()
            
            log.debug(f"[PROCESSING_MANAGER] Processing {len(files_to_process)} files")
            
            # ... existing processing code ...
            
        except Exception as e:
            log.error(f"Error handling process: {e}")
```

### 5. Update Interface Method

Add a new method to the `IUpdateDataView` interface:

```python
class IUpdateDataView(Protocol):
    # ... existing methods ...
    
    def get_current_files(self) -> List[str]:
        """Get the current list of files from the file pane."""
        ...
```

## Testing Plan

1. **Test File Selection**:
   - Select individual files
   - Verify file_paths_list is updated
   - Verify files are displayed in file_pane

2. **Test Folder Selection**:
   - Select a folder
   - Verify file_paths_list is updated with discovered files
   - Verify files are displayed in file_pane

3. **Test File Removal**:
   - Remove a file from file_pane
   - Verify get_current_files() returns updated list

4. **Test Type Safety**:
   - Pass a single string instead of a list
   - Verify defensive checks convert it to a list

5. **Test Processing**:
   - Click process button
   - Verify files from file_pane are used for processing

## Debug Logging Enhancements

Add detailed debug logging at each step:

1. **File Selection**:
   - Log file dialog results
   - Log file_paths_list updates

2. **Folder Selection**:
   - Log folder dialog results
   - Log file discovery results

3. **File Display**:
   - Log file list before and after set_files()
   - Log any type conversions

4. **Processing**:
   - Log files retrieved from file_pane
   - Log files passed to processing

## Implementation Timeline

1. **Phase 1**: Update FileManager with canonical file_paths_list
2. **Phase 2**: Add defensive type checking in all components
3. **Phase 3**: Implement get_current_files() in file_pane
4. **Phase 4**: Update ProcessingManager to use file_pane as source of truth
5. **Phase 5**: Add comprehensive debug logging

This implementation plan ensures a consistent, robust approach to file path handling throughout the Update Data module, with the file_pane as the authoritative source for which files get processed.
