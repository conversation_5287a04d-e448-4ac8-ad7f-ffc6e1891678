"""
File management for Update Data module.

This module consolidates source and archive management logic including:
- File and folder selection
- Save location selection and "same as source" logic
- Folder monitoring
- File discovery and enrichment
- Source and save option changes

Consolidated from SourceManager and ArchiveManager as part of the consolidation refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING

from ....core.services.logger import log
from ..config.option_types import SourceOptions, SaveOptions
from ..services.file_info_service import FileInfoService
from ..services.events_data import SourceDiscoveredEvent, FileDisplayUpdateEvent
from ..services.local_event_bus import ViewEvents

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_manager import UpdateDataState, StateManager


class FileManager:
    """
    Manages all file/folder selection and save location logic.

    This class consolidates:
    - File and folder selection dialogs
    - File enrichment using FileInfoService
    - Folder monitoring integration
    - Save location selection and "same as source" functionality
    - Source and save option changes
    """

    def __init__(self, view: 'IUpdateDataView', state_manager: 'StateManager',
                 folder_monitor_service, local_bus, info_bar_service):
        """
        Initialize the file manager.

        Args:
            view: The view interface
            state_manager: Consolidated state and UI sync manager
            folder_monitor_service: Service for folder monitoring
            local_bus: Local event bus for module events
            info_bar_service: Service for info bar messages
        """
        self.view = view
        self.state_manager = state_manager  # Consolidated state + widget state manager
        self.state = state_manager.state  # Direct access to state data
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # File info service for enriching file data
        self.file_info_service = FileInfoService()
        
        # Track selected source for "same as source" functionality
        self.selected_source = None
        
        # Add canonical file paths list
        self.file_paths_list = []
        log.debug("[FILE_MANAGER] Initialized canonical file_paths_list")

    # =============================================================================
    # SOURCE SELECTION METHODS (from SourceManager)
    # =============================================================================

    def handle_source_select(self, selection_type):
        """
        Handle source selection request from the view.

        Args:
            selection_type: Type of selection (option string from UI)
        """
        log.debug(f"Source selection requested: {selection_type}")

        # Enhanced mapping for UI strings to internal types
        if (selection_type == 'files' or
            selection_type == SourceOptions.SELECT_FILES or
            selection_type == "Select individual files..."):
            self._select_files()
        elif (selection_type == 'folder' or
              selection_type == SourceOptions.SELECT_FOLDER or
              selection_type == "Select entire folder..."):
            self._select_folder()
        else:
            log.warning(f"Unknown selection type: {selection_type}")
            return

        # After source selection, update save location if "same as source"
        self._update_save_location_for_source()

        # Update UI state
        self.state_manager.sync_state_to_view()

    def _select_files(self):
        """Select individual files using file dialog."""
        try:
            # Get last used directory from config
            from ..config.ud_config import ud_config
            from ..config.ud_keys import UpdateDataKeys

            last_dir = ud_config.get_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
            log.debug(f"[FILE_MANAGER] Opening file dialog with last_dir: {last_dir}")

            file_paths = self.view.show_files_dialog("Select CSV Files to Process", last_dir)
            if file_paths:
                # Save the directory for next time
                first_file_dir = os.path.dirname(file_paths[0])
                ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, first_file_dir)
                
                # Store canonical file paths list
                self.file_paths_list = file_paths
                log.debug(f"[FILE_MANAGER] Updated file_paths_list with {len(file_paths)} files")
                
                # Update state
                self.state.selected_files = file_paths
                self.state.source_type = 'files'
                self.selected_source = file_paths
                
                # Enrich file info and display in view
                enriched_info = self.enrich_file_info(file_paths)
                self.view.display_enriched_file_info(enriched_info)
                
                # Update can_process flag
                self.state.update_can_process()
                
                # Pass file paths to file_pane
                if hasattr(self.view, 'center_panel'):
                    log.debug(f"[FILE_MANAGER] Passing {len(self.file_paths_list)} files to center_panel")
                    self.view.center_panel.set_files(self.file_paths_list, "")
                
                # Emit event for file display update
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATE.value, 
                                  FileDisplayUpdateEvent(file_paths=file_paths))
                
                log.debug(f"Selected {len(file_paths)} files")
                
        except Exception as e:
            log.error(f"Error selecting files: {e}")
            log.error(f"[FILE_MANAGER] Error selecting files: {e}")

    def _select_folder(self):
        """Select a folder using folder dialog."""
        try:
            # Get last used directory from config
            from ..config.ud_config import ud_config
            from ..config.ud_keys import UpdateDataKeys

            last_dir = ud_config.get_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
            log.debug(f"[FILE_MANAGER] Opening folder dialog with last_dir: {last_dir}")

            folder_path = self.view.show_folder_dialog("Select Folder Containing CSV Files", last_dir)
            log.debug(f"[FILE_MANAGER] Dialog returned folder_path: {folder_path}")

            if folder_path:
                log.debug(f"[FILE_MANAGER] Processing selected folder: {folder_path}")

                # Save the directory for next time
                ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, folder_path)
                log.debug(f"[FILE_MANAGER] Saved last_dir to config: {folder_path}")

                # Discover files in the folder
                log.debug(f"[FILE_MANAGER] Discovering files in folder...")
                discovered_files = self._discover_files_in_folder(folder_path)
                log.debug(f"[FILE_MANAGER] Discovered {len(discovered_files)} files: {discovered_files}")
                
                # Store canonical file paths list
                self.file_paths_list = discovered_files
                log.debug(f"[FILE_MANAGER] Updated file_paths_list with {len(discovered_files)} files from folder")

                # Update state
                self.selected_source = folder_path
                self.state.selected_folder = folder_path
                self.state.source_type = 'folder'
                
                enriched_files = self.enrich_file_info(discovered_files)
                self.state.selected_files = enriched_files
                self.state.update_can_process()
                log.debug(f"[FILE_MANAGER] State updated - source_type: folder, can_process: {self.state.can_process}")
                
                # Pass file paths to file_pane
                if hasattr(self.view, 'center_panel'):
                    log.debug(f"[FILE_MANAGER] Passing {len(self.file_paths_list)} files to center_panel")
                    self.view.center_panel.set_files(self.file_paths_list, folder_path)

                # Emit file display update event with discovered files
                event_data = FileDisplayUpdateEvent(files=discovered_files, source_path=folder_path)
                log.debug(f"[FILE_MANAGER] Emitting FILE_DISPLAY_UPDATED event: files={len(discovered_files)}, source_path={folder_path}")
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED, event_data)
                log.debug(f"[FILE_MANAGER] FILE_DISPLAY_UPDATED event emitted successfully")

                # Emit source discovered event for folder monitoring
                source_event = SourceDiscoveredEvent(
                    source_type='folder',
                    files=discovered_files,
                    path=folder_path,
                    count=len(discovered_files)
                )
                log.debug(f"[FILE_MANAGER] Emitting SOURCE_DISCOVERED event")
                self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED, source_event)
                log.debug(f"[FILE_MANAGER] SOURCE_DISCOVERED event emitted successfully")

        except Exception as e:
            log.error(f"[FILE_MANAGER] Error selecting folder: {e}")

    def _discover_files_in_folder(self, folder_path: str) -> list:
        """Discover CSV files in the selected folder."""
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                return []

            # Find CSV files in the folder
            csv_files = []
            for file_path in folder.glob("*.csv"):
                if file_path.is_file():
                    csv_files.append(str(file_path))

            log.debug(f"Discovered {len(csv_files)} CSV files in {folder_path}")
            return csv_files

        except Exception as e:
            log.error(f"Error discovering files in folder {folder_path}: {e}")
            return []

    def enrich_file_info(self, file_paths):
        """
        Enrich file paths with additional metadata.

        Args:
            file_paths: List of file paths to enrich

        Returns:
            List of enriched file info dictionaries
        """
        try:
            return self.file_info_service.discover_files(file_paths)
        except Exception as e:
            log.error(f"Error enriching file info: {e}")
            return [{'path': path, 'name': os.path.basename(path)} for path in file_paths]

    def handle_folder_monitor_file_discovered(self, event_data):
        """
        Handle file discovered event from folder monitoring.
        
        Args:
            event_data: Event data containing discovered files
        """
        try:
            if hasattr(event_data, 'files') and event_data.files:
                log.debug(f"Files discovered via folder monitoring: {len(event_data.files)}")
                enriched_files = self.enrich_file_info(event_data.files)
                self.state.selected_files = enriched_files
                self.state.update_can_process()
                
                # Update file display
                source_path = event_data.files[0] if event_data.files else ""
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED,
                                  FileDisplayUpdateEvent(files=enriched_files, source_path=source_path))
                
                # Update UI state
                self.state_manager.sync_state_to_view()
                
        except Exception as e:
            log.error(f"Error handling discovered files: {e}")

    def toggle_folder_monitoring(self):
        """Toggle folder monitoring on/off."""
        try:
            current_state = self.folder_monitor_service.is_monitoring()
            if current_state:
                self.folder_monitor_service.stop_monitoring()
                log.debug("Folder monitoring stopped")
            else:
                if self.state.selected_folder:
                    self.folder_monitor_service.start_monitoring(self.state.selected_folder)
                    log.debug(f"Folder monitoring started for: {self.state.selected_folder}")
                else:
                    log.warning("No folder selected for monitoring")

        except Exception as e:
            log.error(f"Error toggling folder monitoring: {e}")

    def handle_monitor_folder_change(self, enabled):
        """
        Handle folder monitoring enable/disable change.
        
        Args:
            enabled: Boolean indicating if monitoring should be enabled
        """
        try:
            if enabled and self.state.selected_folder:
                self.folder_monitor_service.start_monitoring(self.state.selected_folder)
                log.debug(f"Folder monitoring enabled for: {self.state.selected_folder}")
            else:
                self.folder_monitor_service.stop_monitoring()
                log.debug("Folder monitoring disabled")
                
        except Exception as e:
            log.error(f"Error changing folder monitoring state: {e}")

    def handle_source_option_change(self, option):
        """
        Handle source option changes.

        Args:
            option: The new source option value
        """
        try:
            log.debug(f"Source option changed to: {option}")
            self.state.source_option = option

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling source option change: {e}")

    # =============================================================================
    # SAVE LOCATION METHODS (from ArchiveManager)
    # =============================================================================

    def handle_save_select(self):
        """Handle save location selection request from the view."""
        try:
            save_path = self.view.show_folder_dialog()
            if save_path:
                log.debug(f"Save location selected: {save_path}")
                self.state.save_location = save_path
                self.state.update_can_process()
                
                # Update UI state
                self.state_manager.sync_state_to_view()
                
        except Exception as e:
            log.error(f"Error selecting save location: {e}")

    def handle_save_option_change(self, option):
        """
        Handle save option changes.

        Args:
            option: The new save option value
        """
        try:
            log.debug(f"Save option changed to: {option}")
            self.state.save_option = option

            # Update save location based on option
            self._update_save_location_for_source()

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling save option change: {e}")

    # =============================================================================
    # INTERNAL CONSOLIDATION METHODS
    # =============================================================================

    def _update_save_location_for_source(self):
        """
        Handle 'same as source' logic internally.
        
        This method consolidates the cross-manager communication that was
        previously handled by method wrapping in the presenter.
        """
        try:
            if (self.state.save_option == SaveOptions.SAME_AS_SOURCE and 
                self.selected_source is not None):
                
                if self.state.source_type == 'folder':
                    # For folders, use the folder path directly
                    self.state.save_location = self.selected_source
                    log.debug(f"Save location set to source folder: {self.selected_source}")
                    
                elif self.state.source_type == 'files' and self.selected_source:
                    # For files, use the directory of the first file
                    first_file_path = self.selected_source[0] if isinstance(self.selected_source, list) else self.selected_source
                    save_dir = os.path.dirname(first_file_path)
                    self.state.save_location = save_dir
                    log.debug(f"Save location set to source directory: {save_dir}")
                    
                self.state.update_can_process()
                
        except Exception as e:
            log.error(f"Error updating save location for source: {e}")
