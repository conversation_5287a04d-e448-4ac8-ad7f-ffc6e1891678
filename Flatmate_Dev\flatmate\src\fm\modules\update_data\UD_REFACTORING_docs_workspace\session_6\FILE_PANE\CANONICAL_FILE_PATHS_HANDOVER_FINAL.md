# Canonical File Paths Implementation - Handover Document

## Work Completed

### 1. Core Implementation
- **FileManager**: 
  - Added `file_paths_list` property to maintain a canonical list of file paths
  - Updated `_select_files` and `_select_folder` methods to update the canonical list
  - Added comprehensive debug logging for file path operations
  - Implemented propagation of files to FilePane via `set_files` method

- **FilePane**:
  - Confirmed presence of `set_files` and `get_files` methods with proper logging
  - These methods handle file display management and provide a consistent interface

- **ProcessingManager**:
  - Added `get_files_for_processing()` method to retrieve files directly from FilePane
  - Updated `handle_process()` to use this canonical file list
  - Added fallback to state if FilePane is unavailable
  - Implemented error handling and debug logging

- **Interface**:
  - Added `get_current_files()` method to `IUpdateDataView` interface
  - This ensures a contract for retrieving current file list from view

### 2. Testing
- Created comprehensive unit tests in `test_canonical_file_paths.py`
- Tests cover:
  - FileManager canonical file paths list maintenance
  - FilePane file retrieval
  - ProcessingManager file sourcing
  - Edge cases with mocks and patches
- Fixed patch targets for `ud_config` in tests
- All tests now pass, confirming core canonical file path logic

### 3. Bug Fixes
- Fixed `DEBUG_LEVEL` issue in `ud_presenter.py` by directly setting debug level
- Removed references to non-existent config key

## Current Issues

### 1. File Display Issue
- **Problem**: Selecting entire folder does not populate file display in FilePane
- **Symptoms**: 
  - Files are detected (6 files found in folder)
  - `FILE_DISPLAY_UPDATED` event is emitted successfully
  - `SOURCE_DISCOVERED` event is emitted successfully
  - But no files appear in the file pane UI

### 2. File Handler Matching
- Some files in the selected folder (e.g., `fmMaster_20250731_002307.csv`) don't match any handler
- This is expected behavior for unsupported file formats, but may be confusing to users

## Debugging Options

### 1. File Display Issue

#### Immediate Checks
1. **Event Subscription**: Verify that FilePane is properly subscribed to `FILE_DISPLAY_UPDATED` events
   ```python
   # Check in FilePane initialization or setup method
   self.event_bus.subscribe(UpdateDataEvents.FILE_DISPLAY_UPDATED, self._handle_file_display_update)
   ```

2. **Event Handler Implementation**: Inspect the `_handle_file_display_update` method in FilePane
   ```python
   # Should call set_files with the files from the event
   def _handle_file_display_update(self, event):
       log.debug(f"FilePane received FILE_DISPLAY_UPDATED event with {len(event.files)} files")
       self.set_files(event.files)
   ```

3. **UI Update Logic**: Check if `set_files` properly updates the UI components
   ```python
   # Should update file_list_widget or similar component
   def set_files(self, files):
       log.debug(f"Setting {len(files)} files in FilePane")
       # Clear and repopulate UI components
   ```

#### Advanced Debugging
1. **Add Temporary Debug Logging**:
   ```python
   # In FilePane.set_files method
   log.debug(f"FILES RECEIVED: {files}")
   
   # In FilePane._handle_file_display_update
   log.debug(f"EVENT DATA: {event.__dict__}")
   ```

2. **Event Data Inspection**: Check if the event contains the expected file data
   ```python
   # In FileManager when emitting the event
   log.debug(f"Emitting event with files: {[os.path.basename(f) for f in self.file_paths_list]}")
   ```

3. **UI Component Check**: Verify the file list widget is properly initialized and visible
   ```python
   # In FilePane after updating UI
   log.debug(f"File list widget item count: {self.file_list_widget.count()}")
   ```

### 2. File Handler Issue

1. **Custom Handler**: If needed, implement a custom handler for unrecognized file formats
2. **User Feedback**: Improve UI feedback when files cannot be processed
3. **File Format Detection**: Add more robust file format detection logic

## Next Steps

1. **Fix File Display Issue**:
   - Follow debugging steps above to identify the root cause
   - Implement fix in FilePane or event handling code

2. **Improve Error Handling**:
   - Add more user-friendly error messages for unrecognized file formats
   - Consider adding a "force process" option for non-standard files

3. **Additional Testing**:
   - Create integration tests for the full file selection → display → processing flow
   - Test with various file formats and edge cases

4. **Documentation**:
   - Update developer documentation with canonical file paths design pattern
   - Document expected behavior for unsupported file formats


