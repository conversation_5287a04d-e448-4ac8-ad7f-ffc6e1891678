"""
File pane component for the center panel.
"""

import os

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (QFrame, QLabel, QSizePolicy, QVBoxLayout, QSplitter, QWidget, 
                             QPushButton, QHBoxLayout, QCheckBox)

from fm.gui._shared_components.base.base_pane import BasePane
from fm.gui._shared_components.widgets import SubheadingLabel
from .widgets.file_browser import FileDisplayWidget

class FileBrowser(QFrame):
    """File browser widget for displaying and managing files."""
    
    # Signals for publishing events
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_add_files_requested = Signal()  # Publishes request to add files
    publish_files_added = Signal(list)  # Publishes list of added files
    
    def __init__(self, parent=None):
        """Initialize the file browser."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Use the FileDisplayWidget for showing files
        self.file_display = FileDisplayWidget(self)
        layout.addWidget(self.file_display)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from file display to subscribers
        self.file_display.file_removed.connect(self.publish_file_removed)
        self.file_display.file_selected.connect(self.publish_file_selected)
        self.file_display.add_files_requested.connect(self.publish_add_files_requested)
    
    def set_source_path(self, path: str):
        """Set the source folder path."""
        # No UI update needed here, just for API completeness
        pass
    
    def set_save_path(self, path: str):
        """Set the save location path - now handled by guide pane."""
        # File browser no longer displays this info
        pass
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.
        
        Args:
            files: List of file paths or filenames
            source_dir: Source directory for relative paths
        """
        # Ensure we have valid files to display
        if not files:
            # Clear the display if no files
            self.file_display.set_files([], "")
            return
            
        # Make sure we're working with full paths
        full_paths = []
        for file in files:
            if source_dir and not os.path.isabs(file):
                full_paths.append(os.path.join(source_dir, file))
            else:
                full_paths.append(file)
        
        # Pass the full paths to the file display widget
        self.file_display.set_files(full_paths, source_dir)
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        # Get files from the file display widget
        return self.file_display.get_files() if hasattr(self.file_display, 'get_files') else []


class FilePane(BasePane):
    """Pane component for displaying and managing files."""
    
    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_add_files_requested = Signal()  # Publishes request to add files
    publish_files_added = Signal(list)  # Publishes list of added files
    publish_check_for_new_files_requested = Signal() # Publishes request to scan for new files
    publish_toggle_folder_monitoring_requested = Signal(str, bool)  # Publishes folder path and enabled state
    
    def __init__(self, parent=None):
        """Initialize the file pane."""
        super().__init__(parent)
        self._source_folder_path = None  # Store the source folder path for monitoring
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Set this widget to expand to fill available space
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # Add thin border for debug purposes
        self.setStyleSheet("border: 1px solid #888;")
        # ? >> *is it possible to set style sheet programtically?*
        # Main layout with tighter margins
        layout = QVBoxLayout(self)
        #layout.setContentsMargins(3, 3, 3, 3)  # Tighter margins
        layout.setSpacing(0)  # Tighter spacing

        # Info section removed - this info now belongs in guide pane
        # File pane should only show files, not source/destination context
        
        # Header with label and button
        header_layout = QHBoxLayout()
        self.files_label = SubheadingLabel("Selected Files:")
        header_layout.addWidget(self.files_label)
        header_layout.addStretch()
        
        # Monitor folder checkbox (initially hidden until a folder path is available)
        self.monitor_folder_checkbox = QCheckBox("Monitor Folder")
        self.monitor_folder_checkbox.setToolTip("Automatically detect new files in the source folder")
        self.monitor_folder_checkbox.setVisible(False)  # Hidden by default until a folder path is set
        header_layout.addWidget(self.monitor_folder_checkbox)
        
        self.check_files_button = QPushButton("Check for New Files")
        header_layout.addWidget(self.check_files_button)
        layout.addLayout(header_layout)
        
        # File browser - takes up all available space
        self.file_browser = FileBrowser()
        self.file_browser.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.file_browser, 1)  # The 1 is the stretch factor
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Subscribe to file browser signals
        self.file_browser.publish_file_removed.connect(self.publish_file_removed)
        self.file_browser.publish_file_selected.connect(self.publish_file_selected)
        self.file_browser.publish_add_files_requested.connect(self.publish_add_files_requested)
        self.file_browser.publish_files_added.connect(self.publish_files_added)
        self.check_files_button.clicked.connect(self.publish_check_for_new_files_requested.emit)
        
        # Connect monitor folder checkbox to toggle handler
        self.monitor_folder_checkbox.toggled.connect(self._on_monitor_folder_toggled)
    
    def show_component(self):
        """Show this component."""
        self.show()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def set_source_path(self, path: str):
        """Set the source folder path and show the monitor folder toggle if path exists."""
        # Show the monitor folder checkbox if we have a valid source path
        if path and os.path.exists(path):
            self._source_folder_path = path
            self.monitor_folder_checkbox.setVisible(True)
        else:
            self._source_folder_path = None
            self.monitor_folder_checkbox.setVisible(False)
            
    def _on_monitor_folder_toggled(self, enabled: bool):
        """Handle toggling of the monitor folder checkbox."""
        if self._source_folder_path:
            # Emit signal with folder path and enabled state
            self.publish_toggle_folder_monitoring_requested.emit(self._source_folder_path, enabled)

    def set_save_path(self, path: str):
        """Set the save location path - now handled by guide pane."""
        # File pane no longer displays this info
        pass
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.

        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        log.debug(f"[FILE_PANE] Received set_files call - files: {len(files) if files else 0}, source_dir: {source_dir}")
        log.debug(f"[FILE_PANE] Files list: {files}")

        # Update source path if provided - this will show/hide the monitor checkbox as needed
        if source_dir:
            log.debug(f"[FILE_PANE] Setting source path: {source_dir}")
            self.set_source_path(source_dir)

        log.debug(f"[FILE_PANE] Calling file_browser.set_files()")
        self.file_browser.set_files(files, source_dir)
        log.debug(f"[FILE_PANE] file_browser.set_files() completed")
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_browser.get_files()
    
    def display_enriched_file_info(self, file_info_list):
        """Display enriched file information.
        
        Args:
            file_info_list: List of dictionaries with enriched file information
        """
        if not file_info_list:
            return
            
        # Extract file paths from enriched info
        file_paths = [info.get('path') for info in file_info_list if info.get('path')]
        
        # Update the file browser with the file paths
        if file_paths:
            # Get the source directory from the first file
            source_dir = os.path.dirname(file_paths[0]) if file_paths else ""
            self.file_browser.set_files(file_paths, source_dir)
            
            # Update the files label with enriched info summary
            file_types = set(info.get('format_type', 'Unknown') for info in file_info_list)
            file_types_str = ", ".join(filter(None, file_types)) or "Unknown"
            
            self.files_label.setText(f"{len(file_info_list)} files - Types: {file_types_str}")
            self.files_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
    
    def display_welcome(self):
        """Display welcome message - just clear files."""
        # Reset the file browser by clearing files
        if hasattr(self.file_browser, 'file_display'):
            self.file_browser.set_files([], "")
        self.files_label.setText("Welcome to Update Data")
        self.files_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
    
    def show_error(self, message: str):
        """Show error message."""
        self.files_label.setText(f"Error: {message}")
        self.files_label.setStyleSheet("color: red;")
    
    def show_success(self, message: str):
        """Show success message."""
        self.files_label.setText(message)
        self.files_label.setStyleSheet("color: green;")
